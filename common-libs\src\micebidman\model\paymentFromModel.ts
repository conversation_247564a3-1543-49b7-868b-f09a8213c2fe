import { IPageRequest } from "../../basic";

export class IPaymentFromFilter extends IPageRequest {
    begin?:string
    end?:string
    startTime?:string
    endTime?:string
    serviceProviderName?:string
    merchantCode?:string // 服务商
    status?:number
}


export class IPaymentFrom {
    id?: number | null
    paymentCode?: string // 付款单号
    serviceProviderName?: string // 服务商名称(旧字段)
    merchantName?: string // 服务商名称
    totalAmount?: number // 账单总金额
    settlementRatio?: number // 结算比例(旧字段)
    settlementRate?: number // 结算比例
    paymentAmount?: number // 付款金额
    status?: number // 状态
    creator?: string // 创建人(旧字段)
    createBy?: string // 创建人
    createName?: string // 创建人姓名
    createTime?: string
    updater?: string
    updateTime?: string
    gmtCreate?: string // 创建时间
    gmtModified?: string // 更新时间
    startDate?: string // 需求开始时间(冗余字段)
    endDate?: string // 需求结束时间(冗余字段)
    mainCode?: string // 会议单号(订单号)
    operatorCode?: string // 会议经办人工号
    operatorName?: string // 会议经办人姓名
    amount?: number // 账单金额
    feeRate?: number // 服务费率
    receiveAmount?: number // 收款金额
    attachmentFiles?: any[] // 见证性材料
    paymentRecordsDetails?: any[] // 付款单明细
}

// 创建付款单的参数接口
export interface ICreatePaymentFromParams {
    mainCodes: string[] // 订单号列表
    merchantCode: string // 供应商V码
}

// 确认付款并上传付款凭证的参数接口
export interface IConfirmPaymentParams {
    id: number // 付款单id
    attachmentFile: string[] // 附件路径数组
}

// 驳回付款的参数接口
export interface IRejectPaymentParams {
    id: number // 付款单id
    attachmentFile: string[] // 附件路径数组
    rejectReason?: string // 驳回原因
}

// 上传发票的参数接口
export interface IUploadInvoiceParams {
    paymentId: number // 缴费单id
    paymentCode: string // 缴费单号
    invoiceType?: number // 发票类型 1国旅 2服务商
    invoiceDate: string // 发票日期
    invoiceNumber: string // 发票号
    invoiceAmount: number // 发票金额
}