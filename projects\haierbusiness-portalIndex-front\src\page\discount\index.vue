<script setup lang="ts">
import { Carousel as hCarousel, Card as hCard, message, TypographyText as hTypographyText, Tooltip as hTooltip } from 'ant-design-vue';
import { onMounted, ref, computed } from 'vue';
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
import {
    MerchantTypeConstant
} from '@haierbusiness-front/common-libs';
import { usePagination, useRequest } from 'vue-request';
import { discountApi, tourismProductsApi } from '@haierbusiness-front/apis';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Scrollbar, A11y, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';
import 'swiper/css/autoplay';
import activity from '../card/activity.vue'
import youXuan from '../card/youxuan.vue'
import fire from '@/assets/image/discount/fire.png'
import right from '@/assets/image/discount/right.png'
import desc from '@/assets/image/discount/desc.png'
import tag from '@/assets/image/discount/tag.png'
import electrical from '@/assets/image/discount/electrical.png'
import house from '@/assets/image/discount/house.png'
import rightRound from '@/assets/image/discount/right-round.png'
import leftRound from '@/assets/image/discount/left-round.png'
import shanzhuang from '@/assets/image/discount/shanzhuang.png'
import nanhai from '@/assets/image/discount/nanhai.png'
import canghai from '@/assets/image/discount/canghai.png'
import tehui from '@/assets/image/discount/tehui.png'
import zhouji from '@/assets/image/discount/zhouji.png'
import router from '../../router'
const currentRouter = ref()

const jdUrl = import.meta.env.VITE_JD_PICTURE_URL

const rightCode = ref('')

const modules = ref([Navigation, A11y, Autoplay])

// 机票
const {
    data: flightData,
    run: flightListApiRun,
    loading: flightLoading,
} = useRequest(discountApi.list, {
    defaultParams: [
        {
            merType: MerchantTypeConstant.FLIGHT.code.toString()
        }
    ],
    manual: false
});

// 住宿酒店
const {
    data: hotelData,
    run: hotelListApiRun,
    loading: hotelLoading,
} = useRequest(discountApi.list, {
    defaultParams: [
        {
            merType: MerchantTypeConstant.SIGNING.code.toString()
        }
    ],
    manual: false
});

// 订餐酒店
const {
    data: restaurantData,
    run: restaurantListApiRun,
    loading: restaurantLoading,
} = useRequest(discountApi.list, {
    defaultParams: [
        {
            merType: MerchantTypeConstant.RESTAURANT.code.toString()
        }
    ],
    manual: false
});

// 京东商品
// const {
//   data: jdData,
//   run: jdListApiRun,
//   loading: jdLoading,
// } = useRequest(discountApi.searchProducts, {
//   defaultParams: [
//     {
//         pageIndex: Math.floor(Math.random() * 6),
//         pageSize: 10
//     }
//   ],
//   manual: false
// });

// const jdProduct = computed(() => {
//     if (jdData.value) {
//         return jdData.value.hitResult
//     } else
//         return []
// })

const setRightCode = (value: string) => {
    rightCode.value = value
}

const isFlightSwiperHover = ref(false)

const setIsFlightSwiperHover = (value: boolean) => {
    isFlightSwiperHover.value = value
}

const isHotelSwiperHover = ref(false)

const setIsHotelSwiperHover = (value: boolean) => {
    isHotelSwiperHover.value = value
}

const isRestaurantSwiperHover = ref(false)

const setIsRestaurantSwiperHover = (value: boolean) => {
    isRestaurantSwiperHover.value = value
}

const isJDSwiperHover = ref(false)

const setIsJDSwiperHover = (value: boolean) => {
    isJDSwiperHover.value = value
}

const priceToYuan = (price: number) => {
    if (!price) return 0;
    let yuan = (price / 100).toFixed(2);
    return yuan;
}

const openLink = (url: string) => {
    window.open(url)
}

//旅游产品详情
const {
    data,
    run: listApiRun,
} = useRequest(tourismProductsApi.list);
const dataSource = computed(() => data.value?.records || []);

onMounted(async () => {
    currentRouter.value = await router
    listApiRun({
        pageNum: 1,
        pageSize: 10,
    })
})
const products = ref()
const handleView = async(id:number)=>{
    await get(id)
    if(products.value.externalLinks){
        window.open(products.value.externalLinks, '_blank');
    }else{
        currentRouter.value.push({ path: "/discount/details", query: { id: id } })
    }
    
}
const get = async (id: number) => {
  const data = await tourismProductsApi.get(id)
  if (data && data.id) {
    products.value = data
  }
}

</script>

<template>
    <div class="container">
        <div class="row desc-img margin-top-20 pointer"
            @click="openLink('https://ihaier.feishu.cn/wiki/WyCkws028ilzchkkkL4cGMsanib?fromScene=spaceOverview')">
            <div class="background-img">
                <img :src="desc" class="img-cover" />
            </div>
            <div class="desc-con ">
                <span class="desc">员工因私预订福利来了！个人预订也可享受企业大客户协议价格！</span>
                <div class="small-desc "><span style="border-bottom: 1px solid #fff;">查看更多 ></span></div>
            </div>
        </div>
        <div class="row margin-top-20">
            <div class="ziying">
                <h-card class="ziying-card">
                    <div class="title">
                        <div class="flex left">
                            <div class="flex card-title">自营酒店</div>
                            <div class="flex card-sub-title">海尔员工折上折！</div>
                        </div>
                        <div class="flex right" @mousemove="setRightCode('jingdong')" @mouseleave="setRightCode('')"
                            @click="openLink('https://businesstravel.haier.net/#/login?url=https%3A%2F%2Fbusinesstravel.haier.net%2Flocalhotel%2F%23%2F')">
                            <div class="btn">
                                <div class="btn-font" :class="rightCode === 'jingdong' ? 'to-left' : ''">立即预定</div>
                                <img :src="right" class="btn-right"
                                    :class="rightCode === 'jingdong' ? 'btn-right-show' : ''" />
                            </div>
                        </div>
                    </div>
                    <div class="body">
                        <swiper class="swiper-width" :modules="modules" :slides-per-view="3" :space-between="19"
                            :navigation="{
                                nextEl: '.next_btn',
                                prevEl: '.pre_btn'
                            }" @mousemove="setIsJDSwiperHover(true)" @mouseleave="setIsJDSwiperHover(false)">
                            <swiper-slide class="swiper-slide"
                                @click="openLink('https://businesstravel.haier.net/#/login?url=https%3A%2F%2Fbusinesstravel.haier.net%2Flocalhotel%2F%23%2F')">
                                <div class="merchant-img pointer">
                                    <img :src="shanzhuang" class="img" />
                                    <div class="merchant-title-back"></div>
                                    <div class="merchant-title">
                                        海尔山庄
                                    </div>
                                </div>
                                <div class="discount-desc flex">
                                    <div class="desc flex base-line harmonyBold">
                                        青岛崂山仰口风景区
                                    </div>
                                </div>
                            </swiper-slide>
                            <swiper-slide class="swiper-slide"
                                @click="openLink('https://businesstravel.haier.net/#/login?url=https%3A%2F%2Fbusinesstravel.haier.net%2Flocalhotel%2F%23%2F')">
                                <div class="merchant-img pointer">
                                    <img :src="canghai" class="img" />
                                    <div class="merchant-title-back"></div>
                                    <div class="merchant-title">
                                        沧海之粟
                                    </div>
                                </div>
                                <div class="discount-desc flex">
                                    <div class="desc flex base-line harmonyBold">
                                        青岛崂山区东海东路52号
                                    </div>
                                </div>
                            </swiper-slide>
                            <swiper-slide class="swiper-slide"
                                @click="openLink('https://businesstravel.haier.net/#/login?url=https%3A%2F%2Fbusinesstravel.haier.net%2Flocalhotel%2F%23%2F')">
                                <div class="merchant-img pointer">
                                    <img :src="nanhai" class="img" />
                                    <div class="merchant-title-back"></div>
                                    <div class="merchant-title">
                                        南海德音（南海路）
                                    </div>
                                </div>
                                <div class="discount-desc flex">
                                    <div class="desc flex base-line harmonyBold">
                                        青岛市南区南海路6号
                                    </div>
                                </div>
                            </swiper-slide>
                            <swiper-slide class="swiper-slide"
                                @click="openLink('https://businesstravel.haier.net/#/login?url=https%3A%2F%2Fbusinesstravel.haier.net%2Flocalhotel%2F%23%2F')">
                                <div class="merchant-img pointer">
                                    <img :src="zhouji" class="img" />
                                    <div class="merchant-title-back"></div>
                                    <div class="merchant-title">
                                        海尔洲际酒店
                                    </div>
                                </div>
                                <div class="discount-desc flex">
                                    <div class="desc flex base-line harmonyBold">
                                        青岛市南区澳门路98号
                                    </div>
                                </div>
                            </swiper-slide>
                            <!-- 前进/后退 -->
                            <div class="next_btn" :class="{ hide: !isJDSwiperHover }">
                                <div class="con-btn">
                                    <div class="btn-con">
                                        <right-outlined class="font-size-30" />
                                    </div>
                                </div>
                                <div class="background"></div>
                            </div>
                            <div class="pre_btn" :class="{ hide: !isJDSwiperHover }">
                                <div class="con-btn">
                                    <div class="btn-con">
                                        <left-outlined class="font-size-30" />
                                    </div>
                                </div>
                                <div class="background"></div>
                            </div>
                        </swiper>
                    </div>
                </h-card>
            </div>
            <div class="restaurant">
                <h-card class="restaurant-card">
                    <div class="title">
                        <div class="flex left">
                            <div class="flex card-title">青岛用餐</div>
                            <div class="flex card-sub-title">青岛本地大客户协议！</div>
                        </div>
                        <div class="flex right" @mousemove="setRightCode('restaurant')" @mouseleave="setRightCode('')"
                            @click="openLink('https://businesstravel.haier.net/#/login?url=https%3A%2F%2Fbusinesstravel.haier.net%2Flocalrest%2F%23%2F')">
                            <div class="btn">
                                <div class="btn-font" :class="rightCode === 'restaurant' ? 'to-left' : ''">立即预定</div>
                                <img :src="right" class="btn-right"
                                    :class="rightCode === 'restaurant' ? 'btn-right-show' : ''" />
                            </div>
                        </div>

                    </div>
                    <div class="body">
                        <swiper class="swiper-width" :modules="modules" :slides-per-view="3" :navigation="{
                            nextEl: '.next_btn',
                            prevEl: '.pre_btn'
                        }" @mousemove="setIsRestaurantSwiperHover(true)"
                            @mouseleave="setIsRestaurantSwiperHover(false)">
                            <swiper-slide class="swiper-slide" v-for="(item, index) in restaurantData" :key="index"
                                @click="openLink('https://businesstravel.haier.net/#/login?url=https%3A%2F%2Fbusinesstravel.haier.net%2Flocalrest%2F%23%2F')">
                                <h-tooltip :title="item.details">
                                    <div class="merchant-img pointer">
                                        <img :src="item.icon" class="img" />
                                        <div class="merchant-title-back"></div>
                                        <div class="merchant-title">{{ item.merName }}</div>
                                    </div>
                                    <div class="discount-desc flex">
                                        <div class="desc flex">
                                            企业内购价<span class="red font-size-20 font-weight-600">{{ item.discountDesc
                                                }}</span>
                                        </div>
                                    </div>
                                </h-tooltip>

                            </swiper-slide>
                            <!-- 前进/后退 -->
                            <div class="next_btn" :class="{ hide: !isRestaurantSwiperHover }">
                                <div class="con-btn">
                                    <div class="btn-con">
                                        <right-outlined class="font-size-30" />
                                    </div>
                                </div>
                                <div class="background"></div>
                            </div>
                            <div class="pre_btn" :class="{ hide: !isRestaurantSwiperHover }">
                                <div class="con-btn">
                                    <div class="btn-con">
                                        <left-outlined class="font-size-30" />
                                    </div>
                                </div>
                                <div class="background"></div>
                            </div>
                        </swiper>
                    </div>
                </h-card>
            </div>
        </div>
        <div class="row margin-top-20">
            <div class="flight">
                <h-card class="flight-card">
                    <div class="title">
                        <div class="flex left">
                            <div class="flex fire-img"><img :src="fire" class="fire" /></div>
                            <div class="flex card-title margin-left-12">特惠航司</div>
                            <div class="flex card-sub-title">低价不怕比！海尔员工折上折！</div>
                        </div>
                        <div class="flex right" @mousemove="setRightCode('flight')" @mouseleave="setRightCode('')"
                            @click="openLink('https://travelservice.haier.net/fcc?vessohome=1&cplx=jp&privateBooking=1')">
                            <div class="btn">
                                <div class="btn-font" :class="rightCode === 'flight' ? 'to-left' : ''">立即预订</div>
                                <img :src="right" class="btn-right"
                                    :class="rightCode === 'flight' ? 'btn-right-show' : ''" />
                            </div>
                        </div>

                    </div>
                    <div class="body">
                        <swiper v-if="flightData" class="swiper-width" :modules="modules" :slides-per-view="'auto'"
                            :loop="flightData && flightData.length > 6 ? true : false" :navigation="{
                                nextEl: '.next_btn',
                                prevEl: '.pre_btn'
                            }" @mousemove="setIsFlightSwiperHover(true)" @mouseleave="setIsFlightSwiperHover(false)">
                            <swiper-slide class="swiper-slide" v-for="(item, index) in flightData" :key="index"
                                @click="openLink('https://travelservice.haier.net/fcc?vessohome=1&cplx=jp&privateBooking=1')">
                                <h-tooltip :title="item.details">
                                    <div class="merchant-img pointer">
                                        <img :src="item.icon" class="img" />
                                    </div>
                                    <div class="discount-desc flex">
                                        <div class="desc flex">
                                            {{ item.discountDesc }}
                                        </div>
                                        <img :src="tag" class="discount-image" />
                                    </div>
                                </h-tooltip>
                            </swiper-slide>
                            <!-- 前进/后退 -->
                            <div class="next_btn" :class="{ hide: !isFlightSwiperHover }">
                                <div class="con-btn">
                                    <div class="btn-con">
                                        <right-outlined class="font-size-30" />
                                    </div>
                                </div>
                                <div class="background"></div>
                            </div>
                            <div class="pre_btn" :class="{ hide: !isFlightSwiperHover }">
                                <div class="con-btn">
                                    <div class="btn-con">
                                        <left-outlined class="font-size-30" />
                                    </div>
                                </div>
                                <div class="background"></div>
                            </div>
                        </swiper>
                    </div>
                </h-card>
            </div>

            <div class="hotel">
                <h-card class="hotel-card">
                    <div class="title">
                        <div class="flex left">
                            <div class="flex fire-img"><img :src="fire" class="fire" /></div>
                            <div class="flex card-title margin-left-12">特惠酒店</div>
                            <div class="flex card-sub-title">靠谱又省钱！</div>
                        </div>
                        <div class="flex right" @mousemove="setRightCode('hotel')" @mouseleave="setRightCode('')"
                            @click="openLink('https://travelservice.haier.net/fcc?vessohome=1&cplx=jd&privateBooking=1')">
                            <div class="btn">
                                <div class="btn-font" :class="rightCode === 'hotel' ? 'to-left' : ''">酒店预订</div>
                                <img :src="right" class="btn-right"
                                    :class="rightCode === 'hotel' ? 'btn-right-show' : ''" />
                            </div>
                        </div>

                    </div>
                    <div class="body">

                        <swiper class="swiper-width" :modules="modules" :slides-per-view="3"
                            :loop="hotelData && hotelData.length > 6 ? true : false" :navigation="{
                                nextEl: '.next_btn',
                                prevEl: '.pre_btn'
                            }" @mousemove="setIsHotelSwiperHover(true)" @mouseleave="setIsHotelSwiperHover(false)">
                            <swiper-slide class="swiper-slide" v-for="(item, index) in hotelData" :key="index"
                                @click="openLink('https://travelservice.haier.net/fcc?vessohome=1&cplx=jd&privateBooking=1')">
                                <h-tooltip :title="item.details">
                                    <div class="merchant-img pointer">
                                        <img :src="item.icon" class="img" />
                                    </div>
                                    <div class="discount-desc flex">
                                        <div class="desc flex">
                                            企业内购价<span class="red font-size-20 font-weight-600">{{ item.discountDesc
                                                }}</span>
                                        </div>
                                        <div class="zao">含早</div>
                                    </div>
                                </h-tooltip>
                            </swiper-slide>
                            <!-- 前进/后退 -->
                            <div class="next_btn" :class="{ hide: !isHotelSwiperHover }">
                                <div class="con-btn">
                                    <div class="btn-con">
                                        <right-outlined class="font-size-30" />
                                    </div>
                                </div>
                                <div class="background"></div>
                            </div>
                            <div class="pre_btn" :class="{ hide: !isHotelSwiperHover }">
                                <div class="con-btn">
                                    <div class="btn-con">
                                        <left-outlined class="font-size-30" />
                                    </div>
                                </div>
                                <div class="background"></div>
                            </div>
                        </swiper>
                    </div>
                </h-card>
            </div>
        </div>
        <div class="row margin-top-20">
            <div class="lvyou">
                <h-card class="lvyou-card">
                    <div class="title">
                        <div class="flex left">
                            <div class="flex card-title">旅游产品推荐</div>
                        </div>
                    </div>
                    <div class="body">
                        <swiper class="swiper-width" :modules="modules" :slides-per-view="6" :space-between="0"
                            :navigation="{
                                nextEl: '.next_btn',
                                prevEl: '.pre_btn'
                            }" @mousemove="setIsJDSwiperHover(true)" @mouseleave="setIsJDSwiperHover(false)">
                            <swiper-slide class="swiper-slide pointer" v-for="(item, index) in dataSource" :key="index" @click="handleView(item.id)">
                                <div class="merchant-img pointer">
                                    <img :src="item.path" class="img" />
                                </div>
                                <div class="discount-desc flex">
                                    <div class="desc flex base-line harmonyBold lv-title pointer" >
                                        {{ item.title }}
                                    </div>
                                </div>
                                <div class="lv-content">
                                    联系人：{{ item.nickname }}/{{ item.username }}
                                </div>
                                <div class="lv-price">
                                    <p><span>￥</span>{{ item.staffPrice }}<span>￥{{ item.marketPrice }}</span></p>
                                </div>
                            </swiper-slide>
                            <!-- 前进/后退 -->
                            <div class="next_btn" :class="{ hide: !isJDSwiperHover }">
                                <div class="con-btn">
                                    <div class="btn-con">
                                        <right-outlined class="font-size-30" />
                                    </div>
                                </div>
                                <div class="background"></div>
                            </div>
                            <div class="pre_btn" :class="{ hide: !isJDSwiperHover }">
                                <div class="con-btn">
                                    <div class="btn-con">
                                        <left-outlined class="font-size-30" />
                                    </div>
                                </div>
                                <div class="background"></div>
                            </div>
                        </swiper>
                    </div>
                </h-card>
            </div>
        </div>
        <div class="row">
            <activity />
            <you-xuan />
        </div>

        <div class="row">
            <div class="purchases-con flex">
                <div class="purchases flex">内购权益</div>
                <div class="purchases-desc flex">
                    <div class="electrical flex pointer" @click="openLink('https://hrcare.haier.net/purchase')">
                        <div class="purchases-title flex">
                            <div class="title-left flex"></div>
                            <div class="title flex">家电内购</div>
                        </div>
                        <div class="purchases-info flex">
                            海尔在职员工、直系亲属及退休员工，购买海尔家电享受内购价格约是供价的7-9折，具体价格根据产品类别、产品型号确认。
                        </div>
                        <div class="electrical-img">
                            <img :src="electrical" class="img-cover" />
                        </div>
                    </div>
                    <div class="house flex pointer"
                        @click="openLink('https://businesstravel.haier.net/#/login?url=https%3A%2F%2Fbusinesstravel.haier.net%2Fmall%2F%23%2F')">
                        <div class="purchases-title flex">
                            <div class="title-left flex"></div>
                            <div class="title flex">特惠全球购</div>
                        </div>
                        <div class="purchases-info flex">
                            电商直采，专享员工内购价格
                        </div>
                        <div class="electrical-img">
                            <img :src="tehui" class="img-cover" />
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</template>

<style lang="less">
.harmonyBold {
    font-family: 'HarmonyBold';
}

.row {
    width: 1280px;
    margin-top: 30px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.flex {
    display: flex;
}

.pointer {
    cursor: pointer;
}

.swiper-width {
    width: 100%;
    position: relative;

    .btn-con {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        color: #fff;
        // opacity: 0.8;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;
    }

    .con-btn {
        display: flex;
        width: 100%;
        height: 100%;
        position: relative;
    }

    .background {
        position: absolute;
        background-color: #F0F0F0;
        opacity: 0.8;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 0;
        border-radius: 50%;
    }

    .next_btn {
        position: absolute;
        right: 0;
        top: 59px;
        width: 36px;
        height: 36px;
        z-index: 10;
        cursor: pointer;

        // background: url("../../src/assets/image/discount/right-round.png");
        // background-size: 100%;
    }

    .pre_btn {
        position: absolute;
        left: 0;
        top: 59px;
        width: 36px;
        height: 36px;
        z-index: 10;
    }

    .hide {
        opacity: 0;
    }
}

.red {
    color: #FB0522;
}

.font-size-20 {
    font-size: 20px !important;
}

.font-size-30 {
    font-size: 30px !important;
}

.font-weight-600 {
    font-weight: 600;
}

.margin-top-20 {
    margin-top: 20px !important;
}

.margin-left-12 {
    margin-left: 12px;
}

.base-line {
    align-items: baseline;
}

.img-cover {
    width: 100%;
    height: 100%;
    z-index: 1;
}

.container {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    background-color: #F5F5F5;
    padding-bottom: 45px;

    .desc-img {
        margin-top: 30px;
        height: 60px;

        display: flex;
        align-items: center;
        justify-content: center;
        color: #FFFFFF;


        .desc-con {
            width: 100%;
            display: flex;
            align-items: baseline;
            text-shadow: 0px 1px 1px #E64A00;
            z-index: 1;
            justify-content: space-between;
        }

        .background-img {
            position: absolute;
            width: 1280px;
            height: 60px;
        }

        .small-desc {
            text-align: right;
            padding-right: 20px;
            width: 200px;
            font-size: 14px;

        }

        .desc {
            font-size: 20px;
            padding-left: 20px;
        }
    }

    .title {
        display: flex;
        width: 100%;
        flex-direction: row;
        height: 34px;
        justify-content: space-between;
        font-family: 'HarmonyBold';

        .left {
            flex-direction: row;
            align-items: baseline;

            .fire-img {
                align-items: start;
            }

            .fire {
                width: 22px;
            }

            .card-title {
                font-size: 24px;
                font-weight: bold;
                line-height: 29px;
                align-items: center;
            }

            .card-sub-title {
                margin-left: 8px;
                color: #666666;
                font-size: 14px;
            }
        }

        .right {
            flex-direction: row;


            .btn {
                display: flex;
                border-radius: 4px;
                border: 1px solid #3983E5;
                padding: 6px 10px;
                width: 90px;
                height: 32px;
                justify-content: center;
                color: #3983E5;
                cursor: pointer;
                align-items: center;
                position: relative;
                font-weight: 500;
                font-size: 14px;

                .btn-right {
                    width: 7px;
                    opacity: 0;
                    position: absolute;
                    right: 15px;
                    transition: all .2s;
                }

                .btn-right-show {
                    opacity: 1;
                    right: 10px;
                }

                .btn-font {
                    position: absolute;
                    left: 17px;
                    transition: all .2s;
                }

                .to-left {
                    left: 12px;
                }
            }
        }


    }



    .flight {
        width: 629px;
        height: 324px;

        .flight-card {
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }

        .body {
            width: 100%;
            padding-top: 37px;

            .swiper-slide {
                width: 198px;
                height: 179px;
                padding: 0 13px;
            }

            .swiper-slide:not(:first-child) {
                border-left: 1px #ECECEC solid;
            }

            .merchant-img {
                margin-top: 9px;
                width: 164px;
                height: 117px;

                .img {
                    width: 164px;
                    height: 117px;
                }
            }

            .discount-desc {
                margin-top: 12px;
                width: 117px;
                height: 23px;
                margin-left: 22px;
                flex-direction: row-reverse;
                position: relative;

                .desc {
                    width: 79px;
                    padding: 0 3px;
                    justify-content: center;
                    font-size: 16px;
                    font-weight: 600;
                    color: #FF3D11;
                    line-height: 22px;
                    font-family: 'HarmonyBold';
                    z-index: 1;
                }

                .discount-image {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 117px;
                    height: 23px;
                }
            }
        }
    }

    .hotel {
        width: 629px;
        height: 324px;

        .title {
            padding: 0 12px;
        }

        .hotel-card {
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }

        .body {
            width: 100%;
            padding-top: 37px;

            .swiper-slide {
                width: 198px;
                height: 179px;
                padding: 0 13px;
            }

            .swiper-slide:not(:first-child) {
                border-left: 1px #ECECEC solid;
            }

            .merchant-img {
                margin-top: 9px;
                width: 164px;
                height: 117px;
                position: relative;

                .img {
                    width: 164px;
                    height: 117px;
                }
            }

            .discount-desc {
                margin-top: 12px;
                font-family: 'HarmonyBold';

                .desc {
                    padding: 0 3px;
                    font-size: 14px;
                    color: #000000;
                    line-height: 20px;
                    align-items: baseline;

                }

                .zao {
                    width: 43px;
                    height: 21px;
                    background: linear-gradient(141deg, #FF8D2E 0%, #FF0D0F 100%);
                    border-radius: 10px;
                    font-weight: 400;
                    font-size: 14px;
                    color: #FFFFFF;
                    line-height: 20px;
                    text-align: center;
                }
            }
        }
    }

    .restaurant {
        width: 629px;
        height: 324px;

        .restaurant-card {
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }

        .body {
            width: 100%;
            padding-top: 16px;

            .swiper-slide {
                width: 179px;
                height: 220px;
            }

            .merchant-img {
                width: 179px;
                height: 179px;
                position: relative;
                overflow: hidden;

                .img {
                    width: 179px;
                    height: 179px;
                }

                .merchant-title-back {
                    position: absolute;
                    height: 36px;
                    width: 100%;
                    background: #000000;
                    opacity: 0.4;
                    font-weight: 500;
                    font-size: 14px;
                    color: #FFFFFF;
                    line-height: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    bottom: 0;
                }

                .merchant-title {
                    position: absolute;
                    height: 36px;
                    width: 100%;
                    font-weight: 500;
                    font-size: 14px;
                    color: #FFFFFF;
                    line-height: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    bottom: 0;
                    z-index: 1;
                }
            }

            .discount-desc {
                margin-top: 16px;
                justify-content: center;

                .desc {
                    padding: 0 3px;
                    justify-content: center;
                    font-size: 14px;
                    color: #000000;
                    line-height: 20px;
                    align-items: baseline;
                    font-family: 'HarmonyBold';
                }
            }
        }
    }

    .ziying {
        width: 629px;
        height: 324px;

        .ziying-card {
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }

        .body {
            width: 100%;
            padding-top: 16px;

            .swiper-slide {
                width: 179px;
                height: 213px;
            }

            .merchant-img {
                width: 179px;
                height: 179px;
                position: relative;

                .img {
                    width: 179px;
                    height: 179px;
                }

                .merchant-title-back {
                    position: absolute;
                    height: 36px;
                    width: 100%;
                    background: #000000;
                    opacity: 0.4;
                    font-weight: 500;
                    font-size: 14px;
                    color: #FFFFFF;
                    line-height: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    bottom: 0;
                }

                .merchant-title {
                    position: absolute;
                    height: 36px;
                    width: 100%;
                    font-weight: 500;
                    font-size: 14px;
                    color: #FFFFFF;
                    line-height: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    bottom: 0;
                    z-index: 1;
                }

                .title-width {
                    width: 169px;
                    color: #FFFFFF;
                    font-size: 14px;
                }
            }

            .discount-desc {
                margin-top: 12px;
                justify-content: center;
                font-weight: 500;
                font-size: 14px;
                color: #000000;
            }
        }

    }

    .lvyou {
        width: 100%;
        height: auto;

        .lvyou-card {
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }

        .body {
            width: 100%;
            padding-top: 16px;

            .swiper-slide {
                width: 179px;
                height: 300px;
            }

            .merchant-img {
                width: 179px;
                height: 179px;
                position: relative;

                .img {
                    width: 179px;
                    height: 179px;
                }

                .merchant-title-back {
                    position: absolute;
                    height: 36px;
                    width: 100%;
                    background: #000000;
                    opacity: 0.4;
                    font-weight: 500;
                    font-size: 14px;
                    color: #FFFFFF;
                    line-height: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    bottom: 0;
                }

                .merchant-title {
                    position: absolute;
                    height: 36px;
                    width: 100%;
                    font-weight: 500;
                    font-size: 14px;
                    color: #FFFFFF;
                    line-height: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    bottom: 0;
                    z-index: 1;
                }

                .title-width {
                    width: 169px;
                    color: #FFFFFF;
                    font-size: 14px;
                }
            }

            .discount-desc {
                margin-top: 12px;
                font-weight: 500;
                font-size: 14px;
                color: #000000;
            }
            .lv-title{
                height: 44px;
                width: 179px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .lv-content{
                color: #7a7a7a;
                font-size: 12px;
                margin-top: 10px;
            }
            .lv-price{
                margin-top: 10px;
                p{
                    color: red;
                    font-weight: 500px;
                    font-size: 17px;
                    span{
                        color: #7a7a7a;
                        font-size: 10px;
                        text-decoration: line-through;
                    }
                    span:nth-child(1){
                        color: red;
                        text-decoration: none;
                    }
                }
            }
        }

    }

    .purchases-con {
        flex-direction: column;
        width: 100%;

        .purchases {
            color: #000;
            font-size: 24px;
            font-weight: 600;
            line-height: 20px;
            width: 100%;
        }

        .purchases-desc {
            flex-direction: row;
            justify-content: space-between;
            margin-top: 10px;

            .electrical {
                height: 211px;
                width: 629px;
                padding: 38px 204px 62px 34px;
                flex-direction: column;
                position: relative;
                border-radius: 9px;
            }

            .electrical-img {
                position: absolute;
                height: 211px;
                width: 629px;
                top: 0;
                left: 0;
            }

            .house {
                height: 211px;
                width: 629px;
                padding: 38px 204px 62px 34px;
                flex-direction: column;
                z-index: 1;
                position: relative;
                border-radius: 9px;
            }

            .purchases-title {
                flex-direction: row;
                z-index: 1;

                .title-left {
                    width: 4px;
                    height: 21px;
                    background: linear-gradient(180deg, #6BB9F4 0%, #3983E5 100%);
                    border-radius: 2px;
                }

                .title {
                    font-weight: 600;
                    font-size: 18px;
                    color: #000000;
                    margin-left: 12px;
                }
            }

            .purchases-info {
                width: 100%;
                font-size: 14px;
                color: #666666;
                line-height: 21px;
                z-index: 1;
            }
        }
    }



}
</style>

<style>
.hotel .ant-card-body {
    padding: 24px 12px;
}
</style>
