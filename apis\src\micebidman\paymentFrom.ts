import { download, get, post } from '../request'
import {
    IPaymentFromFilter,
    IPaymentFrom,
    ICreatePaymentFromParams,
    IConfirmPaymentParams,
    IRejectPaymentParams,
    IUploadInvoiceParams,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'


export const paymentFromApi = {
    // 查询服务商所有的结算单信息
    getBillList: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/payment/getBillList', params)
    },
    // 收款单列表查询
    getPage: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/payment/getPage', params)
    },
    // 收款单详情查询
    getDetails: (id: number): Promise<IPaymentFrom> => {
        return get('/mice-bid/api/mice/payment/getDetails', {
            id
        })
    },
    // 创建付款单
    create: (params: ICreatePaymentFromParams): Promise<Result> => {
        return post('/mice-bid/api/mice/payment/create', params)
    },
    // 财务确认付款并上传付款凭证
    confirmPayment: (params: IConfirmPaymentParams): Promise<Result> => {
        return post('/mice-bid/api/mice/payment/confirmPayment', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('merchant/api/paymentFrom/delete', { id })
    },

    // 查询服务商所有的结算单信息-缴费
    getBalnceList: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/plat/receive/payment/getBalnceList', params)
    },
    // 收款单列表查询-缴费
    getPaymentPage: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/plat/receive/payment/getPage', params)
    },
    // 收款单详情查询-缴费
    getPaymentDetails: (id: number): Promise<IPaymentFrom> => {
        return get('/mice-bid/api/mice/plat/receive/payment/getDetails', {
            id
        })
    },
    // 创建付款单-缴费
    createPayment: (params: ICreatePaymentFromParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/create', params)
    },
    // 财务确认付款并上传付款凭证-缴费
    confirmPaymentPayment: (params: IConfirmPaymentParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/confirm/receivePayment', params)
    },
    // 财务驳回付款-缴费
    rejectPayment: (params: IRejectPaymentParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/reject/receivePayment', params)
    },
    // 上传发票-缴费
    uploadInvoice: (params: IUploadInvoiceParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/upload/invoice', params)
    },
    // 删除收款单-缴费
    removePayment: (id: number): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/delete', { id })
    },

}
