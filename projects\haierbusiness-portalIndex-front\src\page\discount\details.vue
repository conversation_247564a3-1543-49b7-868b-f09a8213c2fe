<template>
  <div class="contioner">
    <h1>{{ Products.title }}</h1>
    <p class="gray" style="margin: 15px 0;">{{ Products.briefIntroduction }}</p>
    <div class="img-content">
      <div class="img-left">
        <img :src="Products.path" alt="">
      </div>
      <div class="img-right">
        <table>
          <tr>
            <td class="bold">价格</td>
            <td>
              <div class="lv-price">
                <p><span>￥</span>{{ Products.staffPrice }}<span>￥{{ Products.marketPrice }}</span></p>
              </div>
            </td>
          </tr>
          <tr>
            <td class="bold">产品特色</td>
            <td>
              <a-tag v-for="item in tagList(Products.tag)" :key="item" color="blue" style="margin:3px">
            {{ item }}
          </a-tag>
            </td>
          </tr>
          <tr>
            <td class="bold">出发地</td>
            <td>
              {{ Products.startCity }}
            </td>
          </tr>
          <tr>
            <td class="bold">联系人</td>
            <td>
              {{ Products.nickname }}/{{ Products.username }}
            </td>
          </tr>
        </table>
      </div>
    </div>
    <div v-html="Products.detail" style="margin-top: 25px;"></div>
  </div>
</template>
<script lang="ts" setup>
import { tourismProductsApi } from "@haierbusiness-front/apis";
import { computed, ref, watch, onMounted } from "vue";
import { useRoute, useRouter } from 'vue-router'
import router from "../../router";
import { ITourismProducts } from "@haierbusiness-front/common-libs";

const Products = ref<ITourismProducts>({});
const currentRouter = ref()
const id = ref<number>()

onMounted(async () => {
  currentRouter.value = await router
  const currentId = currentRouter.value.currentRoute.query?.id
  id.value = Number(currentId)

  if (id.value) {
    await get(id.value)
  }
})

const get = async (id: number) => {
  const data = await tourismProductsApi.get(id)
  if (data && data.id) {
    Products.value = data
  }
}

const tagList = (list: string | undefined): string[] => {
  if (!list) return []
  return list
    .split('，') // 使用中文逗号分隔
    .map(item => item.trim())
    .filter(item => item.length > 0) // 过滤空字符串
}
</script>
<style lang="less" scoped>
*{
  font-family: PingFangSC, PingFang SC;
}
.contioner{
  width: 1280px;
  margin: 0 auto;
  padding: 40px 0;

  h1{
    font-size: 24px;
    font-weight: bold;
  }
  .img-content{
    display: flex;
    justify-content: space-between;
  }
  .img-left{
    img{
      width: 220px;
      height: 220px;
    }
  }
  .img-right{
    width: 80%;
    background-color: #fef9ee;
    height: 170px;
  }
}
.lv-price {
  height: 100%;
  p {
    color: red;
    font-weight: 500px;
    font-size: 20px;
    height: 100%;

    span {
      color: #7a7a7a;
      font-size: 12px;
      text-decoration: line-through;
    }

    span:nth-child(1) {
      color: red;
      text-decoration: none;
    }
  }
}
.gray{
  color: #7a7a7a;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.bold{
  font-weight: 400;
  font-size: 12px;
  color: #7a7a7a;
}
table tr {
  height: 40px;  /* 固定行高 */
  line-height: 1.5; /* 文本行高 */
}
table td {
  padding: 0 8px; /* 上下12px 左右8px */
}
</style>